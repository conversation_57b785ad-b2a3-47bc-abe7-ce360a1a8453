import AsyncStorage from '@react-native-async-storage/async-storage';
import ApiService from './ApiService';
import DatabaseIntegrationService from './DatabaseIntegrationService';

export interface CachedRecipe {
  id: string;
  title: string;
  description: string;
  ingredients: string[];
  instructions: string[];
  nutrition: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  cookTime: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  servings: number;
  tags: string[];
  imageUrl: string;
  source: 'weekly_plan' | 'generated' | 'manual';
  cachedAt: number;
  lastAccessed: number;
}

class RecipeCacheService {
  private static instance: RecipeCacheService;
  private readonly RECIPE_CACHE_KEY = 'cached_recipes';
  private readonly CACHE_EXPIRY_DAYS = 30; // Recipes expire after 30 days
  private readonly MAX_CACHED_RECIPES = 100; // Maximum recipes to keep in cache

  static getInstance(): RecipeCacheService {
    if (!RecipeCacheService.instance) {
      RecipeCacheService.instance = new RecipeCacheService();
    }
    return RecipeCacheService.instance;
  }

  // Generate a consistent recipe ID based on meal name
  private generateRecipeId(mealName: string): string {
    return `recipe_${mealName.toLowerCase().replace(/[^a-z0-9]/g, '_')}`;
  }

  // Check if recipe exists in cache
  async isCached(mealName: string): Promise<boolean> {
    try {
      const recipeId = this.generateRecipeId(mealName);
      const cachedRecipes = await this.getCachedRecipes();
      const recipe = cachedRecipes.find(r => r.id === recipeId);
      
      if (!recipe) return false;
      
      // Check if recipe is expired
      const isExpired = this.isRecipeExpired(recipe);
      if (isExpired) {
        await this.removeFromCache(recipeId);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('❌ Error checking recipe cache:', error);
      return false;
    }
  }

  // Get recipe from cache
  async getCachedRecipe(mealName: string): Promise<CachedRecipe | null> {
    try {
      const recipeId = this.generateRecipeId(mealName);
      const cachedRecipes = await this.getCachedRecipes();
      const recipe = cachedRecipes.find(r => r.id === recipeId);
      
      if (!recipe) return null;
      
      // Check if recipe is expired
      if (this.isRecipeExpired(recipe)) {
        await this.removeFromCache(recipeId);
        return null;
      }
      
      // Update last accessed time
      recipe.lastAccessed = Date.now();
      await this.updateRecipeInCache(recipe);
      
      console.log(`📖 Retrieved cached recipe: ${mealName}`);
      return recipe;
    } catch (error) {
      console.error('❌ Error getting cached recipe:', error);
      return null;
    }
  }

  // Generate and cache new recipe
  async generateAndCacheRecipe(mealName: string, source: 'weekly_plan' | 'generated' = 'generated'): Promise<CachedRecipe> {
    try {
      console.log(`🔄 Generating new recipe for: ${mealName}`);
      
      // Generate recipe using API
      const apiRecipe = await ApiService.generateRecipe(mealName);
      
      // Convert to cached recipe format
      const cachedRecipe: CachedRecipe = {
        id: this.generateRecipeId(mealName),
        title: apiRecipe.recipeTitle,
        description: `Delicious ${mealName.toLowerCase()} recipe with ${apiRecipe.estimatedCalories} calories`,
        ingredients: apiRecipe.ingredients,
        instructions: apiRecipe.steps,
        nutrition: {
          calories: apiRecipe.estimatedCalories,
          protein: parseFloat(apiRecipe.macros.protein.replace('g', '')) || 25,
          carbs: parseFloat(apiRecipe.macros.carbs.replace('g', '')) || 30,
          fat: parseFloat(apiRecipe.macros.fats.replace('g', '')) || 15,
        },
        cookTime: '30 min',
        difficulty: 'Medium',
        servings: 4,
        tags: apiRecipe.tags,
        imageUrl: apiRecipe.imageUrl || 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=800&h=600&fit=crop&crop=center&q=80',
        source,
        cachedAt: Date.now(),
        lastAccessed: Date.now()
      };
      
      // Cache the recipe
      await this.addToCache(cachedRecipe);
      
      // Also save to database for backup
      try {
        await DatabaseIntegrationService.syncRecipeToDatabase({
          title: cachedRecipe.title,
          ingredients: cachedRecipe.ingredients,
          instructions: cachedRecipe.instructions,
          nutrition: cachedRecipe.nutrition,
          tags: cachedRecipe.tags,
          imageUrl: cachedRecipe.imageUrl,
          cookTime: cachedRecipe.cookTime,
          difficulty: cachedRecipe.difficulty,
          servings: cachedRecipe.servings,
          isFavorite: false
        });
      } catch (dbError) {
        console.warn('⚠️ Failed to save recipe to database:', dbError);
      }
      
      console.log(`✅ Generated and cached recipe: ${mealName}`);
      return cachedRecipe;
    } catch (error) {
      console.error(`❌ Failed to generate recipe for ${mealName}:`, error);

      // Return sophisticated fallback instead of throwing
      console.log(`🔄 Using sophisticated fallback recipe for: ${mealName}`);
      return await this.createFallbackRecipe(mealName, source);
    }
  }

  // Get or generate recipe (main method) - with improved caching
  async getOrGenerateRecipe(mealName: string, source: 'weekly_plan' | 'generated' = 'generated'): Promise<CachedRecipe> {
    const recipeId = this.generateRecipeId(mealName);

    try {
      // First check cache with detailed logging
      console.log(`🔍 Checking cache for recipe: ${mealName} (ID: ${recipeId})`);
      const cachedRecipe = await this.getCachedRecipe(mealName);

      if (cachedRecipe) {
        console.log(`📖 Cache HIT - Using cached recipe for: ${mealName}`);
        return cachedRecipe;
      }

      // Generate new recipe if not cached
      console.log(`📝 Cache MISS - Generating new recipe for: ${mealName}`);
      const newRecipe = await this.generateAndCacheRecipe(mealName, source);
      console.log(`✅ Recipe generated and cached: ${mealName}`);
      return newRecipe;

    } catch (error) {
      console.error(`❌ Error in getOrGenerateRecipe for ${mealName}:`, error);

      // Try to return a basic fallback recipe instead of throwing
      console.log(`🔄 Attempting to create fallback recipe for: ${mealName}`);
      return await this.createFallbackRecipe(mealName, source);
    }
  }

  // Create a sophisticated fallback recipe when API fails
  private async createFallbackRecipe(mealName: string, source: 'weekly_plan' | 'generated'): Promise<CachedRecipe> {
    // Create more sophisticated fallback recipes based on meal name
    const mealLower = mealName.toLowerCase();
    let sophisticatedRecipe = this.generateSophisticatedFallback(mealLower);

    // Get dynamic Unsplash image
    const imageUrl = await this.getUnsplashFallbackImage(mealName);

    const fallbackRecipe: CachedRecipe = {
      id: this.generateRecipeId(mealName),
      title: sophisticatedRecipe.title,
      description: sophisticatedRecipe.description,
      ingredients: sophisticatedRecipe.ingredients,
      instructions: sophisticatedRecipe.instructions,
      nutrition: sophisticatedRecipe.nutrition,
      cookTime: sophisticatedRecipe.cookTime,
      difficulty: sophisticatedRecipe.difficulty,
      servings: 4,
      tags: sophisticatedRecipe.tags,
      imageUrl: imageUrl,
      source,
      cachedAt: Date.now(),
      lastAccessed: Date.now()
    };

    // Cache the fallback recipe for future use
    this.addToCache(fallbackRecipe).catch(error => {
      console.error('❌ Failed to cache fallback recipe:', error);
    });

    return fallbackRecipe;
  }

  // Generate sophisticated fallback recipes based on meal type
  private generateSophisticatedFallback(mealName: string): {
    title: string;
    description: string;
    ingredients: string[];
    instructions: string[];
    nutrition: { calories: number; protein: number; carbs: number; fat: number };
    cookTime: string;
    difficulty: 'Easy' | 'Medium' | 'Hard';
    tags: string[];
  } {
    // Sophisticated recipe templates based on common meal types
    if (mealName.includes('chicken') || mealName.includes('poultry')) {
      return {
        title: `Herb-Crusted ${mealName}`,
        description: `Tender, juicy chicken with aromatic herbs and spices, perfectly seasoned and cooked to perfection`,
        ingredients: [
          '2 chicken breasts (6 oz each)',
          '2 tbsp olive oil',
          '1 tsp garlic powder',
          '1 tsp dried thyme',
          '1 tsp dried rosemary',
          '1/2 tsp paprika',
          'Salt and black pepper to taste',
          '1 lemon (juiced)'
        ],
        instructions: [
          'Preheat oven to 375°F (190°C)',
          'Pat chicken breasts dry and season with salt and pepper',
          'Mix herbs, garlic powder, and paprika in a small bowl',
          'Rub chicken with olive oil, then coat with herb mixture',
          'Place in baking dish and drizzle with lemon juice',
          'Bake for 25-30 minutes until internal temperature reaches 165°F',
          'Let rest for 5 minutes before slicing',
          'Serve hot with your favorite sides'
        ],
        nutrition: { calories: 320, protein: 35, carbs: 2, fat: 18 },
        cookTime: '35 min',
        difficulty: 'Easy' as const,
        tags: ['protein-rich', 'healthy', 'gluten-free', 'low-carb']
      };
    }

    if (mealName.includes('salmon') || mealName.includes('fish')) {
      return {
        title: `Pan-Seared ${mealName}`,
        description: `Perfectly cooked salmon with crispy skin and tender, flaky interior, seasoned with fresh herbs`,
        ingredients: [
          '4 salmon fillets (5 oz each)',
          '2 tbsp olive oil',
          '1 tbsp butter',
          '2 cloves garlic (minced)',
          '1 lemon (sliced)',
          'Fresh dill sprigs',
          'Salt and pepper to taste',
          '1 tsp smoked paprika'
        ],
        instructions: [
          'Remove salmon from refrigerator 15 minutes before cooking',
          'Pat fillets dry and season with salt, pepper, and paprika',
          'Heat olive oil in a large skillet over medium-high heat',
          'Place salmon skin-side up and cook for 4-5 minutes',
          'Flip carefully and add butter and garlic to pan',
          'Cook for 3-4 minutes more, basting with butter',
          'Add lemon slices and dill in final minute',
          'Serve immediately with pan juices'
        ],
        nutrition: { calories: 380, protein: 42, carbs: 1, fat: 22 },
        cookTime: '15 min',
        difficulty: 'Medium' as const,
        tags: ['omega-3', 'healthy', 'quick', 'protein-rich']
      };
    }

    if (mealName.includes('salad') || mealName.includes('bowl')) {
      return {
        title: `Fresh ${mealName}`,
        description: `Vibrant, nutrient-packed salad with fresh vegetables, protein, and a delicious homemade dressing`,
        ingredients: [
          '4 cups mixed greens',
          '1 cup cherry tomatoes (halved)',
          '1 cucumber (diced)',
          '1/2 red onion (thinly sliced)',
          '1/4 cup feta cheese (crumbled)',
          '2 tbsp olive oil',
          '1 tbsp balsamic vinegar',
          '1 tsp Dijon mustard',
          'Salt and pepper to taste'
        ],
        instructions: [
          'Wash and dry all vegetables thoroughly',
          'Combine mixed greens, tomatoes, cucumber, and onion in large bowl',
          'Whisk together olive oil, balsamic vinegar, and Dijon mustard',
          'Season dressing with salt and pepper',
          'Drizzle dressing over salad and toss gently',
          'Top with crumbled feta cheese',
          'Serve immediately for best texture',
          'Add protein of choice if desired'
        ],
        nutrition: { calories: 180, protein: 8, carbs: 12, fat: 14 },
        cookTime: '10 min',
        difficulty: 'Easy' as const,
        tags: ['fresh', 'healthy', 'vegetarian', 'quick']
      };
    }

    // Default sophisticated fallback
    return {
      title: `Gourmet ${mealName}`,
      description: `A delicious and nutritious ${mealName.toLowerCase()} prepared with fresh ingredients and expert techniques`,
      ingredients: [
        'Fresh main ingredients',
        'High-quality seasonings',
        'Extra virgin olive oil',
        'Fresh herbs',
        'Sea salt and cracked pepper',
        'Complementary vegetables',
        'Natural flavor enhancers'
      ],
      instructions: [
        'Prepare all ingredients by washing and chopping as needed',
        'Heat cooking surface to appropriate temperature',
        'Season main ingredients generously with salt and pepper',
        'Cook using proper technique for optimal flavor and texture',
        'Add herbs and seasonings during cooking process',
        'Monitor cooking progress and adjust heat as needed',
        'Finish with fresh herbs or garnish',
        'Serve hot and enjoy immediately'
      ],
      nutrition: { calories: 350, protein: 25, carbs: 20, fat: 18 },
      cookTime: '25 min',
      difficulty: 'Medium' as const,
      tags: ['gourmet', 'healthy', 'balanced', 'nutritious']
    };
  }

  // Get a dynamic Unsplash image using API call
  private async getUnsplashFallbackImage(mealName: string): Promise<string> {
    const searchTerm = mealName.toLowerCase().replace(/[^a-z0-9\s]/g, '').trim();
    const accessKey = 'QPySZeLMRd2Rw0BKoNKpXFwrHY0aSVZMwxvTmZaIZEs';
    const unsplashApiUrl = 'https://api.unsplash.com';

    try {
      console.log(`🖼️ Fetching Unsplash image for: ${mealName}`);

      // Use user's exact search term for most relevant results
      const unsplashUrl = `${unsplashApiUrl}/search/photos?query=${encodeURIComponent(searchTerm)}&orientation=landscape&order_by=relevant&per_page=1&client_id=${accessKey}`;

      const response = await fetch(unsplashUrl);

      if (response.ok) {
        const data = await response.json();

        if (data.results && data.results.length > 0) {
          const mostRelevantPhoto = data.results[0];
          const imageUrl = mostRelevantPhoto.urls.regular;
          console.log(`✅ Found Unsplash image for ${mealName}: ${imageUrl}`);
          return imageUrl;
        } else {
          console.log(`⚠️ No specific results for ${mealName}, trying generic food search`);
          // Fallback to generic food search
          const genericUrl = `${unsplashApiUrl}/search/photos?query=food&orientation=landscape&order_by=relevant&per_page=5&client_id=${accessKey}`;
          const genericResponse = await fetch(genericUrl);

          if (genericResponse.ok) {
            const genericData = await genericResponse.json();
            if (genericData.results && genericData.results.length > 0) {
              const imageUrl = genericData.results[0].urls.regular;
              console.log(`✅ Using generic food image for ${mealName}: ${imageUrl}`);
              return imageUrl;
            }
          }

          throw new Error('No search results found');
        }
      } else {
        console.error('❌ Unsplash API error:', response.status, response.statusText);
        throw new Error(`Unsplash API error: ${response.status}`);
      }
    } catch (error) {
      console.warn(`⚠️ Failed to fetch Unsplash image for ${mealName}:`, error);

      // Return specific static images based on meal type as final fallback
      if (searchTerm.includes('chicken')) {
        return `https://images.unsplash.com/photo-1598103442097-8b74394b95c6?w=800&h=600&fit=crop&crop=center&q=80&auto=format&cs=tinysrgb&ixlib=rb-4.0.3`;
      } else if (searchTerm.includes('salmon') || searchTerm.includes('fish')) {
        return `https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=800&h=600&fit=crop&crop=center&q=80&auto=format&cs=tinysrgb&ixlib=rb-4.0.3`;
      } else if (searchTerm.includes('salad')) {
        return `https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=800&h=600&fit=crop&crop=center&q=80&auto=format&cs=tinysrgb&ixlib=rb-4.0.3`;
      } else if (searchTerm.includes('pasta')) {
        return `https://images.unsplash.com/photo-1551892374-ecf8754cf8b0?w=800&h=600&fit=crop&crop=center&q=80&auto=format&cs=tinysrgb&ixlib=rb-4.0.3`;
      } else {
        // Default high-quality food image
        return `https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=800&h=600&fit=crop&crop=center&q=80&auto=format&cs=tinysrgb&ixlib=rb-4.0.3`;
      }
    }
  }

  // Private helper methods
  private async getCachedRecipes(): Promise<CachedRecipe[]> {
    try {
      const cached = await AsyncStorage.getItem(this.RECIPE_CACHE_KEY);
      return cached ? JSON.parse(cached) : [];
    } catch (error) {
      console.error('❌ Error getting cached recipes:', error);
      return [];
    }
  }

  private async saveCachedRecipes(recipes: CachedRecipe[]): Promise<void> {
    try {
      await AsyncStorage.setItem(this.RECIPE_CACHE_KEY, JSON.stringify(recipes));
    } catch (error) {
      console.error('❌ Error saving cached recipes:', error);
    }
  }

  private async addToCache(recipe: CachedRecipe): Promise<void> {
    try {
      const recipes = await this.getCachedRecipes();
      
      // Remove existing recipe with same ID
      const filteredRecipes = recipes.filter(r => r.id !== recipe.id);
      
      // Add new recipe
      filteredRecipes.unshift(recipe);
      
      // Limit cache size
      const limitedRecipes = filteredRecipes.slice(0, this.MAX_CACHED_RECIPES);
      
      await this.saveCachedRecipes(limitedRecipes);
    } catch (error) {
      console.error('❌ Error adding recipe to cache:', error);
    }
  }

  private async updateRecipeInCache(updatedRecipe: CachedRecipe): Promise<void> {
    try {
      const recipes = await this.getCachedRecipes();
      const index = recipes.findIndex(r => r.id === updatedRecipe.id);
      
      if (index !== -1) {
        recipes[index] = updatedRecipe;
        await this.saveCachedRecipes(recipes);
      }
    } catch (error) {
      console.error('❌ Error updating recipe in cache:', error);
    }
  }

  private async removeFromCache(recipeId: string): Promise<void> {
    try {
      const recipes = await this.getCachedRecipes();
      const filteredRecipes = recipes.filter(r => r.id !== recipeId);
      await this.saveCachedRecipes(filteredRecipes);
    } catch (error) {
      console.error('❌ Error removing recipe from cache:', error);
    }
  }

  private isRecipeExpired(recipe: CachedRecipe): boolean {
    const expiryTime = recipe.cachedAt + (this.CACHE_EXPIRY_DAYS * 24 * 60 * 60 * 1000);
    return Date.now() > expiryTime;
  }

  // Cleanup expired recipes
  async cleanupExpiredRecipes(): Promise<void> {
    try {
      const recipes = await this.getCachedRecipes();
      const validRecipes = recipes.filter(recipe => !this.isRecipeExpired(recipe));
      
      if (validRecipes.length !== recipes.length) {
        await this.saveCachedRecipes(validRecipes);
        console.log(`🧹 Cleaned up ${recipes.length - validRecipes.length} expired recipes`);
      }
    } catch (error) {
      console.error('❌ Error cleaning up expired recipes:', error);
    }
  }

  // Get cache statistics
  async getCacheStats(): Promise<{ totalRecipes: number; cacheSize: string }> {
    try {
      const recipes = await this.getCachedRecipes();
      const cacheData = await AsyncStorage.getItem(this.RECIPE_CACHE_KEY);
      const sizeInBytes = cacheData ? new Blob([cacheData]).size : 0;
      const sizeInKB = (sizeInBytes / 1024).toFixed(2);

      return {
        totalRecipes: recipes.length,
        cacheSize: `${sizeInKB} KB`
      };
    } catch (error) {
      console.error('❌ Error getting cache stats:', error);
      return { totalRecipes: 0, cacheSize: '0 KB' };
    }
  }

  // Get all cached recipes (for debugging/admin purposes)
  async getAllCachedRecipes(): Promise<CachedRecipe[]> {
    return await this.getCachedRecipes();
  }

  // Force regenerate a specific recipe (bypass cache)
  async forceRegenerateRecipe(mealName: string, source: 'weekly_plan' | 'generated' = 'generated'): Promise<CachedRecipe> {
    try {
      console.log(`🔄 Force regenerating recipe for: ${mealName}`);

      // Remove from cache first
      await this.removeFromCache(mealName);

      // Generate new recipe
      return await this.generateAndCacheRecipe(mealName, source);
    } catch (error) {
      console.error(`❌ Error force regenerating recipe for ${mealName}:`, error);
      return await this.createFallbackRecipe(mealName, source);
    }
  }

  // Clear all cached recipes
  async clearAllCache(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.RECIPE_CACHE_KEY);
      console.log('🧹 All recipe cache cleared');
    } catch (error) {
      console.error('❌ Error clearing recipe cache:', error);
    }
  }

  // Get recipes by source
  async getRecipesBySource(source: 'weekly_plan' | 'generated'): Promise<CachedRecipe[]> {
    try {
      const recipes = await this.getCachedRecipes();
      return recipes.filter(recipe => recipe.source === source);
    } catch (error) {
      console.error('❌ Error getting recipes by source:', error);
      return [];
    }
  }
}

export default RecipeCacheService.getInstance();
