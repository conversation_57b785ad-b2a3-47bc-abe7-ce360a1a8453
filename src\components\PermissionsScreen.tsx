import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  StatusBar,
  ScrollView,
  Platform,
  PermissionsAndroid,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { Audio } from 'expo-av';
import * as Location from 'expo-location';
import {
  Pedometer,
  Barometer,
  LightSensor,
  Gyroscope,
  Accelerometer,
  Magnetometer
} from 'expo-sensors';
// Removed react-native-permissions due to build issues
// Using only native Android methods and Expo APIs
import NotificationService from '../services/NotificationService';
import { PermissionUtils } from '../utils/PermissionUtils';
import Animated, {
  FadeInUp,
  FadeInDown,
  SlideInLeft,
  ZoomIn,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

interface PermissionsScreenProps {
  onPermissionsGranted: () => void;
}

interface Permission {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  granted: boolean;
  redirectToSettings?: boolean;
  requestFunction: () => Promise<boolean>;
}

// Function to open app permissions page specifically
const openAppPermissionsPage = async () => {
  try {
    if (Platform.OS === 'android') {
      // Try to open the app permissions page directly
      const packageName = 'com.saim3333.nutriai'; // Your app's package name
      const url = `android-app://com.android.settings/android.settings.APPLICATION_DETAILS_SETTINGS?package=${packageName}`;

      const canOpen = await Linking.canOpenURL(url);
      if (canOpen) {
        await Linking.openURL(url);
        return true;
      } else {
        // Fallback to general app settings
        await Linking.openSettings();
        return true;
      }
    } else {
      // For iOS, use the standard settings
      await Linking.openSettings();
      return true;
    }
  } catch (error) {
    console.error('❌ Error opening app permissions page:', error);
    // Final fallback to general settings
    try {
      await Linking.openSettings();
      return true;
    } catch (fallbackError) {
      console.error('❌ Error opening settings fallback:', fallbackError);
      return false;
    }
  }
};

const PermissionsScreen = ({ onPermissionsGranted }: PermissionsScreenProps) => {
  const [permissions, setPermissions] = useState([
    {
      id: 'camera',
      title: 'Camera Access',
      description: 'Scan food items and take photos for meal tracking',
      icon: 'camera',
      color: '#6B7C5A',
      granted: false,
      requestFunction: async () => {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        return status === 'granted';
      },
    },
    {
      id: 'microphone',
      title: 'Microphone Access',
      description: 'Voice commands and audio input for AI assistant',
      icon: 'mic',
      color: '#8B9A7A',
      granted: false,
      requestFunction: async () => {
        const { status } = await Audio.requestPermissionsAsync();
        return status === 'granted';
      },
    },
    {
      id: 'photos',
      title: 'Photo Library',
      description: 'Select photos from your gallery for meal analysis',
      icon: 'images',
      color: '#6B7C5A',
      granted: false,
      requestFunction: async () => {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        return status === 'granted';
      },
    },
    {
      id: 'location',
      title: 'Location Access',
      description: 'Find nearby restaurants and local nutrition data',
      icon: 'location',
      color: '#8B9A7A',
      granted: false,
      requestFunction: async () => {
        const { status } = await Location.requestForegroundPermissionsAsync();
        return status === 'granted';
      },
    },
    {
      id: 'notifications',
      title: 'Notifications',
      description: 'Receive meal reminders and achievement notifications',
      icon: 'notifications',
      color: '#6B7C5A',
      granted: false,
      requestFunction: async () => {
        try {
          console.log('🔍 Requesting notifications permission using latest methods...');

          // Use the NotificationService which handles the latest notification permission methods
          const granted = await NotificationService.requestPermissions();
          console.log('📱 Notifications permission result:', granted);

          if (!granted) {
            Alert.alert(
              'Notifications Required',
              'Notification permissions are required for meal reminders and health tracking alerts. Please enable them in Settings.',
              [
                { text: 'Cancel', style: 'cancel' },
                {
                  text: 'Open Settings',
                  onPress: () => Linking.openSettings()
                }
              ]
            );
          }

          return granted;
        } catch (error) {
          console.error('❌ Error requesting notifications permission:', error);
          return false;
        }
      },
    },
    {
      id: 'sensors',
      title: 'Body Sensors',
      description: 'Track heart rate and health metrics',
      icon: 'fitness',
      color: '#6B7C5A',
      granted: false,
      redirectToSettings: Platform.OS === 'android',
      requestFunction: async () => {
        if (Platform.OS === 'ios') {
          // On iOS, try to request sensor permissions directly
          try {
            const result = await PermissionUtils.requestHealthSensorPermissions();
            return result.granted;
          } catch (error) {
            console.error('iOS sensor permission error:', error);
            return false;
          }
        } else {
          // Android - redirect to app permissions page
          console.log('🔄 Opening app permissions page for Body Sensors permission...');
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          await openAppPermissionsPage();
          return false;
        }
      },
    },
    {
      id: 'activity',
      title: 'Physical Activity',
      description: 'Monitor steps and physical activity',
      icon: 'walk',
      color: '#8B9A7A',
      granted: false,
      redirectToSettings: Platform.OS === 'android',
      requestFunction: async () => {
        if (Platform.OS === 'ios') {
          // On iOS, activity tracking is part of sensor permissions
          try {
            const result = await PermissionUtils.requestHealthSensorPermissions();
            return result.granted;
          } catch (error) {
            console.error('iOS activity permission error:', error);
            return false;
          }
        } else {
          // Android - redirect to app permissions page
          console.log('🔄 Opening app permissions page for Physical Activity permission...');
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          await openAppPermissionsPage();
          return false;
        }
      },
    },
  ]);

  const [allGranted, setAllGranted] = useState(false);

  useEffect(() => {
    checkAllPermissions();

    // Add app state listener to refresh permissions when returning from settings
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'active') {
        console.log('📱 App became active, refreshing permission status...');
        setTimeout(() => {
          checkAllPermissions();
        }, 500); // Small delay to ensure permissions are updated
      }
    };

    // Note: AppState listener would be added here in a full implementation
    // For now, we'll rely on manual refresh
  }, []);

  const checkAllPermissions = async () => {
    const updatedPermissions = await Promise.all(
      permissions.map(async (permission) => {
        let granted = false;
        try {
          switch (permission.id) {
            case 'camera':
              const cameraStatus = await ImagePicker.getCameraPermissionsAsync();
              granted = cameraStatus.status === 'granted';
              break;
            case 'microphone':
              const audioStatus = await Audio.getPermissionsAsync();
              granted = audioStatus.status === 'granted';
              break;
            case 'photos':
              const mediaStatus = await ImagePicker.getMediaLibraryPermissionsAsync();
              granted = mediaStatus.status === 'granted';
              break;
            case 'location':
              const locationStatus = await Location.getForegroundPermissionsAsync();
              granted = locationStatus.status === 'granted';
              break;
            case 'sensors':
            case 'activity':
              // For Android, these permissions require manual settings navigation
              // Never auto-grant these permissions - always require user action
              if (Platform.OS === 'android') {
                granted = false; // Always false to ensure user can click to open settings
                console.log(`🔒 ${permission.id} permission: REQUIRES MANUAL SETTINGS (Android)`);
              } else {
                // For iOS, check actual permission status
                try {
                  const healthStatus = await PermissionUtils.checkHealthSensorPermissions();
                  granted = healthStatus.granted;
                  console.log(`💓 ${permission.id} permission: ${granted ? 'GRANTED' : 'DENIED'} (iOS)`);
                  console.log(`🔧 Method used: ${healthStatus.method}`);
                } catch (error) {
                  console.log(`⚠️ Could not check ${permission.id} permissions:`, error);
                  granted = false;
                }
              }
              break;
            case 'notifications':
              // Check notification permissions without requesting them
              const notificationStatus = await NotificationService.getPermissionStatus();
              granted = notificationStatus === 'granted';
              console.log(`🔔 Notification permission: ${granted ? 'GRANTED' : 'DENIED'} (status: ${notificationStatus})`);
              break;
          }
        } catch (error) {
          console.log(`Error checking ${permission.id} permission:`, error);
        }
        return { ...permission, granted };
      })
    );

    setPermissions(updatedPermissions);
    const allPermissionsGranted = updatedPermissions.every(p => p.granted);
    setAllGranted(allPermissionsGranted);
  };

  const requestPermission = async (permission: Permission) => {
    try {
      console.log(`🔍 User requesting ${permission.title} permission...`);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      if (permission.redirectToSettings) {
        // For settings-redirect permissions, just open settings
        console.log(`🔄 Redirecting to settings for ${permission.title}...`);
        await permission.requestFunction();

        // Show helpful message
        Alert.alert(
          `${permission.title} Settings`,
          `Please enable ${permission.title} permission in your device settings. The app will automatically detect the permission when you return.`,
          [{ text: 'Got it', style: 'default' }]
        );
        return;
      }

      const granted = await permission.requestFunction();
      console.log(`📱 ${permission.title} permission result: ${granted ? 'GRANTED' : 'DENIED'}`);

      setPermissions(prev =>
        prev.map(p =>
          p.id === permission.id ? { ...p, granted } : p
        )
      );

      if (!granted) {
        console.log(`⚠️ ${permission.title} permission denied by user`);
        Alert.alert(
          'Permission Required',
          `${permission.title} is needed for the best app experience. You can enable it later in Settings.`,
          [{ text: 'OK' }]
        );
      } else {
        console.log(`✅ ${permission.title} permission granted successfully`);
      }

      // Check if all permissions are now granted
      const updatedPermissions = permissions.map(p =>
        p.id === permission.id ? { ...p, granted } : p
      );
      const allPermissionsGranted = updatedPermissions.every(p => p.granted);
      setAllGranted(allPermissionsGranted);

      console.log(`📊 All permissions status: ${allPermissionsGranted ? 'ALL GRANTED' : 'SOME PENDING'}`);
    } catch (error) {
      console.error(`❌ Error requesting ${permission.title}:`, error);
      Alert.alert('Error', `Failed to request ${permission.title}. Please try again.`);
    }
  };

  const handleContinue = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    onPermissionsGranted();
  };

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor="transparent"
        translucent={Platform.OS === 'android'}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <Animated.View entering={FadeInDown.duration(800)} style={styles.header}>
          <Animated.View entering={ZoomIn.delay(400).duration(600)} style={styles.iconContainer}>
            <Ionicons name="shield-checkmark" size={48} color="#6B7C5A" />
          </Animated.View>
          <Text style={styles.title}>App Permissions</Text>
          <Text style={styles.subtitle}>
            Grant permissions to unlock all features and get the best experience
          </Text>
        </Animated.View>



        {/* Permissions List */}
        <View style={styles.permissionsList}>
          {permissions.map((permission, index) => (
            <Animated.View
              key={permission.id}
              entering={SlideInLeft.delay(600 + index * 150).duration(600)}
              style={styles.permissionCard}
            >
              <View style={styles.permissionContent}>
                <View style={[styles.permissionIcon, { backgroundColor: `${permission.color}15` }]}>
                  <Ionicons name={permission.icon as any} size={24} color={permission.color} />
                </View>

                <View style={styles.permissionText}>
                  <Text style={styles.permissionTitle}>{permission.title}</Text>
                  <Text style={styles.permissionDescription}>{permission.description}</Text>
                </View>

                <TouchableOpacity
                  style={[
                    styles.permissionButton,
                    permission.granted && styles.permissionButtonGranted,
                    permission.redirectToSettings && !permission.granted && styles.permissionButtonSettings
                  ]}
                  onPress={() => requestPermission(permission)}
                  disabled={permission.granted}
                >
                  {permission.granted ? (
                    <Ionicons name="checkmark" size={20} color="#FFFFFF" />
                  ) : permission.redirectToSettings ? (
                    <Ionicons name="settings" size={20} color="#6B7C5A" />
                  ) : (
                    <Ionicons name="add" size={20} color="#6B7C5A" />
                  )}
                </TouchableOpacity>
              </View>
            </Animated.View>
          ))}
        </View>



        {/* Continue Button */}
        <Animated.View entering={FadeInUp.delay(1200).duration(600)} style={styles.continueContainer}>
          <TouchableOpacity
            style={[styles.continueButton, allGranted && styles.continueButtonEnabled]}
            onPress={handleContinue}
          >
            <Text style={[styles.continueText, allGranted && styles.continueTextEnabled]}>
              {allGranted ? 'Continue to App' : 'Continue Anyway'}
            </Text>
            <Ionicons
              name="arrow-forward"
              size={20}
              color={allGranted ? '#FFFFFF' : '#6B7C5A'}
            />
          </TouchableOpacity>

          {!allGranted && (
            <Text style={styles.skipText}>
              You can grant permissions later in Settings
            </Text>
          )}
        </Animated.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingTop: 80,
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 32,
    fontWeight: '800',
    color: '#6B7C5A',
    textAlign: 'center',
    marginBottom: 12,
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#8B9A7A',
    textAlign: 'center',
    lineHeight: 24,
  },
  permissionsList: {
    gap: 16,
    marginBottom: 32,
  },
  permissionCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  permissionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  permissionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  permissionText: {
    flex: 1,
  },
  permissionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
  },
  permissionDescription: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
    lineHeight: 20,
  },
  permissionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#6B7C5A',
  },
  permissionButtonGranted: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
  permissionButtonSettings: {
    backgroundColor: 'rgba(139, 154, 122, 0.1)',
    borderColor: '#8B9A7A',
  },
  continueContainer: {
    paddingTop: 24,
  },
  continueButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 32,
    borderRadius: 32,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderWidth: 2,
    borderColor: '#6B7C5A',
    gap: 12,
  },
  continueButtonEnabled: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
  continueText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#6B7C5A',
    letterSpacing: 0.5,
  },
  continueTextEnabled: {
    color: '#FFFFFF',
  },
  skipText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#8B9A7A',
    textAlign: 'center',
    marginTop: 16,
  },
  refreshContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 20,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.3)',
    gap: 8,
  },
  refreshText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7C5A',
  },
});

export default PermissionsScreen;
